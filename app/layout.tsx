import type { Metada<PERSON> } from "next";
import "./globals.css";
import { Providers } from "@/components/SessionProvider";

export const metadata: Metadata = {
  title: "Spotify Player",
  description: "A simple audio player using Spotify API",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="antialiased">
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
