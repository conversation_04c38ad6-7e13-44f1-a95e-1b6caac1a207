'use client';

import { useSession, signIn, signOut } from 'next-auth/react';
import { useState, useEffect, useCallback } from 'react'
import { SpotifyAPI, SpotifyTrack, SpotifyPlaylist } from '@/lib/spotify'
import { SessionManager } from '@/components/SessionManager'
import { AudioPlayer } from '@/components/AudioPlayer'
import { PersistenceService, SavedSession } from '@/lib/persistence'
import { usePlayback } from '@/contexts/PlaybackContext'
import { SaveSessionModal } from '@/components/SaveSessionModal'
import { LoadSessionModal } from '@/components/LoadSessionModal'

interface Column {
    id: string
    title: string
    trackIds: string[]
}

export default function Home() {
    const { data: session, status } = useSession()
    const playback = usePlayback()
    const [playlists, setPlaylists] = useState<SpotifyPlaylist[]>([])
    const [selectedPlaylist, setSelectedPlaylist] =
        useState<SpotifyPlaylist | null>(null)
    const [tracks, setTracks] = useState<SpotifyTrack[]>([])
    const [hasRestoredState, setHasRestoredState] = useState(false)
    const [shownModal, setShownModal] = useState<'save' | 'load' | undefined>()
    const [savedSessions, setSavedSessions] = useState<SavedSession[]>([])

    const [currentSessionData, setCurrentSessionData] = useState<{
        columns: Column[]
        lastTrackId?: string
    } | null>(null)
    const [loadSessionTrigger, setLoadSessionTrigger] =
        useState<SavedSession | null>(null)

    useEffect(() => {
        if (session?.accessToken) {
            fetchPlaylists()
            playback.initializePlayer(session.accessToken)
        }
    }, [session?.accessToken])

    // Restore app state from localStorage on mount
    useEffect(() => {
        if (!hasRestoredState && session?.accessToken && playlists.length > 0) {
            const appState = PersistenceService.loadState()
            if (appState?.lastPlaylistId) {
                const playlist = playlists.find(
                    (p) => p.id === appState.lastPlaylistId
                )
                if (playlist) {
                    selectPlaylist(playlist)
                }
            }
            setHasRestoredState(true)
        }
    }, [session, playlists, hasRestoredState])

    // Save current playlist to localStorage when it changes
    useEffect(() => {
        if (selectedPlaylist) {
            const currentState = PersistenceService.loadState() || {
                savedSessions: []
            }
            PersistenceService.saveState({
                ...currentState,
                lastPlaylistId: selectedPlaylist.id
            })
        }
    }, [selectedPlaylist])

    const fetchPlaylists = useCallback(async () => {
        if (!session?.accessToken) return

        const api = new SpotifyAPI(session.accessToken)
        try {
            const data = await api.getPlaylists()
            setPlaylists(data.items)
        } catch (error) {
            console.error('Failed to fetch playlists:', error)
        }
    }, [session?.accessToken])

    const selectPlaylist = async (playlist: SpotifyPlaylist) => {
        if (!session?.accessToken) return

        const api = new SpotifyAPI(session.accessToken)
        try {
            const fullPlaylist = await api.getPlaylist(playlist.id)
            setSelectedPlaylist(fullPlaylist)

            // Handle the tracks properly - the SDK returns a Page with items
            if (fullPlaylist.tracks && 'items' in fullPlaylist.tracks) {
                const tracks = fullPlaylist.tracks.items
                    .map((item) => item.track)
                    .filter(
                        (track): track is SpotifyTrack => track.type === 'track'
                    ) // Filter out episodes
                setTracks(tracks)

                // Restore last selected track if available
                const autoSession = PersistenceService.loadAutoSession()
                if (
                    autoSession &&
                    autoSession.playlistId === playlist.id &&
                    autoSession.lastTrackId
                ) {
                    const lastTrack = tracks.find(
                        (t) => t.id === autoSession.lastTrackId
                    )
                    if (lastTrack) {
                        playback.playTrack(lastTrack)
                    }
                }
            }
        } catch (error) {
            console.error('Failed to fetch playlist tracks:', error)
        }
    }

    const handleSaveSession = (sessionName: string) => {
        if (selectedPlaylist && currentSessionData) {
            PersistenceService.saveSession(sessionName, {
                playlistId: selectedPlaylist.id,
                columns: currentSessionData.columns,
                lastTrackId: currentSessionData.lastTrackId
            })
        }
    }

    const handleLoadSession = (session: SavedSession) => {
        setLoadSessionTrigger(session)
        // Reset trigger after a short delay to allow SessionManager to process it
        setTimeout(() => setLoadSessionTrigger(null), 500)
    }

    const handleDeleteSession = (sessionId: string) => {
        PersistenceService.deleteSession(sessionId)
        // Refresh the sessions list
        if (selectedPlaylist) {
            const sessions = PersistenceService.getSavedSessions().filter(
                (s) => s.playlistId === selectedPlaylist.id
            )
            setSavedSessions(sessions)
        }
    }

    const handleSessionDataUpdate = useCallback(
        (data: { columns: Column[]; lastTrackId?: string }) => {
            setCurrentSessionData(data)
        },
        []
    )

    const openLoadModal = () => {
        if (selectedPlaylist) {
            const sessions = PersistenceService.getSavedSessions().filter(
                (s) => s.playlistId === selectedPlaylist.id
            )
            setSavedSessions(sessions)
            setShownModal('load')
        }
    }

    if (status === 'loading') {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-xl">Loading...</div>
            </div>
        )
    }

    if (!session) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-gray-100">
                <div className="bg-white p-8 rounded-lg shadow-lg">
                    <h1 className="text-3xl font-bold mb-4">
                        Session Audio Manager
                    </h1>
                    <p className="mb-6 text-gray-600">
                        Sign in with your Spotify account to manage audio
                    </p>
                    <button
                        onClick={() => signIn('spotify')}
                        className="bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-6 rounded-full"
                    >
                        Sign in with Spotify
                    </button>
                </div>
            </div>
        )
    }

    return (
        <div className="flex flex-col h-screen bg-gray-50">
            <header className="bg-white border-b border-gray-200 px-6 py-3">
                <div className="flex justify-between items-center">
                    <div className="flex items-center gap-4">
                        <select
                            value={selectedPlaylist?.id || ''}
                            onChange={(e) => {
                                const playlist = playlists.find(
                                    (p) => p.id === e.target.value
                                )
                                if (playlist) selectPlaylist(playlist)
                            }}
                            className="text-xl font-bold text-gray-900 bg-transparent border-2 border-gray-300 rounded-lg px-3 py-1 focus:outline-none focus:border-blue-500"
                        >
                            <option value="">Select Playlist</option>
                            {playlists.map((playlist) => (
                                <option key={playlist.id} value={playlist.id}>
                                    {playlist.name}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div className="flex items-center gap-4">
                        {selectedPlaylist && (
                            <>
                                <button
                                    onClick={() => setShownModal('save')}
                                    className="px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-medium transition-colors flex items-center gap-2 text-sm"
                                >
                                    <svg
                                        width="16"
                                        height="16"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                    >
                                        <path d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h5a2 2 0 012 2v7a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h5v5.586l-1.293-1.293zM9 4a1 1 0 012 0v2H9V4z" />
                                    </svg>
                                    Save
                                </button>
                                <button
                                    onClick={openLoadModal}
                                    className="px-3 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg font-medium transition-colors flex items-center gap-2 text-sm"
                                >
                                    <svg
                                        width="16"
                                        height="16"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                    >
                                        <path
                                            fillRule="evenodd"
                                            d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                                            clipRule="evenodd"
                                        />
                                    </svg>
                                    Load
                                </button>
                            </>
                        )}
                        <button
                            onClick={() => signOut()}
                            className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                        >
                            Sign Out
                        </button>
                    </div>
                </div>
            </header>

            <main className="flex-1 overflow-hidden">
                {selectedPlaylist && tracks.length > 0 ? (
                    <SessionManager
                        availableTracks={tracks}
                        playlistId={selectedPlaylist.id}
                        onSessionDataUpdate={handleSessionDataUpdate}
                        loadSessionTrigger={loadSessionTrigger}
                    />
                ) : (
                    <div className="flex-1 flex items-center justify-center">
                        <div className="text-center">
                            <svg
                                className="mx-auto h-12 w-12 text-gray-400 mb-4"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={1.5}
                                    d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"
                                />
                            </svg>
                            <p className="text-gray-500 font-medium">
                                Select a playlist from the dropdown above
                            </p>
                        </div>
                    </div>
                )}
            </main>

            <div className="border-t">
                <AudioPlayer accessToken={session.accessToken!} />
            </div>

            <SaveSessionModal
                isOpen={shownModal === 'save'}
                onClose={() => setShownModal(undefined)}
                onSave={handleSaveSession}
            />

            <LoadSessionModal
                isOpen={shownModal === 'load'}
                onClose={() => setShownModal(undefined)}
                sessions={savedSessions}
                onLoad={handleLoadSession}
                onDelete={handleDeleteSession}
            />
        </div>
    )
}
