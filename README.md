# Session Audio Manager

A NextJS application for managing audio during tabletop RPG sessions, board game nights, or any activity requiring organized background music. Connect to your Spotify account to organize tracks into ambient, combat, and custom categories.

## Features

- **Multi-Column Layout**: Organize tracks into Ambient, Combat, and custom categories
- **Drag & Drop**: Move tracks between columns with Trello-like interface
- **Playlist Selection**: Quick dropdown selector for Spotify playlists
- **Custom Columns**: Add unlimited custom columns for different scenarios
- **Audio Playback**: Play/pause tracks using Spotify Web Playback SDK
- **Clean Interface**: Minimalist design optimized for session management

## Prerequisites

Before you begin, ensure you have:

- Node.js 18+ installed
- A Spotify Premium account (required for Web Playback SDK)
- A Spotify App created at [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)

## Setup Instructions

### 1. Clone the repository

```bash
cd spotify-player
```

### 2. Install dependencies

```bash
npm install
```

### 3. Configure Spotify App

1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Create a new app or select an existing one
3. In your app settings, add the following Redirect URI:
   - **Use**: `http://127.0.0.1:3000/api/auth/callback/spotify`
   - **Important**: Spotify no longer accepts `localhost` - you must use the explicit IP address `127.0.0.1`
   - Note: You'll see a security warning for HTTP, but this is fine for local development
4. Copy your Client ID and Client Secret

### 4. Set up environment variables

Create or update the `.env.local` file with your Spotify credentials:

```env
SPOTIFY_CLIENT_ID=your_spotify_client_id_here
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret_here
NEXTAUTH_URL=http://127.0.0.1:3000
NEXTAUTH_SECRET=generate_a_random_secret_key_here
```

To generate a secure `NEXTAUTH_SECRET`, you can use:
```bash
openssl rand -base64 32
```

### 5. Run the development server

```bash
npm run dev
```

**Important**: Open [http://127.0.0.1:3000](http://127.0.0.1:3000) in your browser (NOT localhost:3000).

## Usage

1. **Sign In**: Click "Sign in with Spotify" and authorize the app
2. **Select Playlist**: Choose a playlist from the dropdown menu
3. **Organize Tracks**: Drag tracks from "Available Tracks" to Ambient, Combat, or custom columns
4. **Create Columns**: Click the "+" button to add custom categories (e.g., "Tavern", "Boss Fight")
5. **Play Music**: Click the play button on any track
6. **Control Playback**: Use the player controls at the bottom

## Important Notes

- **Spotify Premium Required**: The Web Playback SDK requires a Spotify Premium account
- **Active Device**: Make sure you have an active Spotify device or the web player open
- **Browser Support**: Works best in Chrome, Firefox, Edge, and Safari
- **Redirect URI**: Must use `http://127.0.0.1:3000` (not localhost) as Spotify no longer accepts localhost URLs
- **Access the app**: Always use `http://127.0.0.1:3000` in your browser, not `localhost:3000`

## Technologies Used

- **Next.js 14**: React framework with App Router
- **TypeScript**: Type-safe development
- **NextAuth.js**: Authentication with Spotify OAuth
- **Spotify Web API**: Playlist and track data
- **Spotify Web Playback SDK**: In-browser audio playback
- **@dnd-kit**: Drag and drop functionality
- **Tailwind CSS**: Styling and responsive design

## Troubleshooting

### No audio playback
- Ensure you have Spotify Premium
- Check that your browser allows autoplay
- Try refreshing the page after sign-in

### Playlists not showing
- Make sure your Spotify account has playlists
- Check that the app has proper permissions

### Authentication issues
- Verify your `.env.local` credentials are correct
- Ensure redirect URI matches exactly in Spotify App settings
