import {
    SpotifyApi,
    Track,
    SimplifiedPlaylist,
    Playlist
} from '@spotify/web-api-ts-sdk'

// Re-export types from the SDK with our custom alias field
export interface SpotifyTrack extends Track {
    alias?: string
}

// Use SimplifiedPlaylist for the list view, Playlist for full details
export type SpotifyPlaylist = SimplifiedPlaylist | Playlist

export class SpotifyAPI {
    private api: SpotifyApi

    constructor(accessToken: string) {
        this.api = SpotifyApi.withAccessToken(process.env.SPOTIFY_CLIENT_ID!, {
            access_token: accessToken,
            token_type: 'Bearer',
            expires_in: 3600,
            refresh_token: ''
        })
    }

    async getPlaylist(playlistId: string): Promise<SpotifyPlaylist> {
        return await this.api.playlists.getPlaylist(playlistId)
    }

    async getPlaylists(): Promise<{ items: SimplifiedPlaylist[] }> {
        const response = await this.api.currentUser.playlists.playlists()
        return { items: response.items }
    }

    async play(deviceId: string, uri?: string, position?: number) {
        if (uri) {
            await this.api.player.startResumePlayback(
                deviceId,
                undefined,
                [uri],
                { position_ms: position }
            )
        } else {
            await this.api.player.startResumePlayback(deviceId)
        }
    }

    async pause(deviceId: string) {
        await this.api.player.pausePlayback(deviceId)
    }

    async getDevices() {
        return await this.api.player.getAvailableDevices()
    }
}
