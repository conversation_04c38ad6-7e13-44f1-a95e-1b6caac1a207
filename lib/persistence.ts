export interface SavedSession {
  id: string;
  name: string;
  playlistId: string;
  columns: {
    id: string;
    title: string;
    trackIds: string[];
  }[];
  lastTrackId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AppState {
  currentSession?: SavedSession;
  savedSessions: SavedSession[];
  lastPlaylistId?: string;
}

const STORAGE_KEY = 'session_audio_manager_state';
const AUTO_SAVE_KEY = 'session_audio_manager_autosave';

export class PersistenceService {
  static saveState(state: AppState): void {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
    } catch (error) {
      console.error('Failed to save state:', error);
    }
  }

  static loadState(): AppState | null {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        return JSON.parse(saved);
      }
    } catch (error) {
      console.error('Failed to load state:', error);
    }
    return null;
  }

  static saveAutoSession(session: Omit<SavedSession, 'id' | 'createdAt' | 'updatedAt'>): void {
    try {
      const autoSession: SavedSession = {
        ...session,
        id: 'autosave',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      localStorage.setItem(AUTO_SAVE_KEY, JSON.stringify(autoSession));
    } catch (error) {
      console.error('Failed to autosave session:', error);
    }
  }

  static loadAutoSession(): SavedSession | null {
    try {
      const saved = localStorage.getItem(AUTO_SAVE_KEY);
      if (saved) {
        return JSON.parse(saved);
      }
    } catch (error) {
      console.error('Failed to load autosave:', error);
    }
    return null;
  }

  static clearAutoSession(): void {
    try {
      localStorage.removeItem(AUTO_SAVE_KEY);
    } catch (error) {
      console.error('Failed to clear autosave:', error);
    }
  }

  static saveSession(name: string, session: Omit<SavedSession, 'id' | 'name' | 'createdAt' | 'updatedAt'>): string {
    const state = this.loadState() || { savedSessions: [] };

    const newSession: SavedSession = {
      ...session,
      id: `session_${Date.now()}`,
      name,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    state.savedSessions.push(newSession);
    state.currentSession = newSession;

    this.saveState(state);
    return newSession.id;
  }

  static loadSession(sessionId: string): SavedSession | null {
    const state = this.loadState();
    if (!state) return null;

    const session = state.savedSessions.find(s => s.id === sessionId);
    if (session) {
      state.currentSession = session;
      this.saveState(state);
    }
    return session || null;
  }

  static deleteSession(sessionId: string): void {
    const state = this.loadState();
    if (!state) return;

    state.savedSessions = state.savedSessions.filter(s => s.id !== sessionId);
    if (state.currentSession?.id === sessionId) {
      state.currentSession = undefined;
    }

    this.saveState(state);
  }

  static getSavedSessions(): SavedSession[] {
    const state = this.loadState();
    return state?.savedSessions || [];
  }

  static updateSession(sessionId: string, updates: Partial<SavedSession>): void {
    const state = this.loadState();
    if (!state) return;

    const index = state.savedSessions.findIndex(s => s.id === sessionId);
    if (index !== -1) {
      state.savedSessions[index] = {
        ...state.savedSessions[index],
        ...updates,
        updatedAt: new Date().toISOString(),
      };

      if (state.currentSession?.id === sessionId) {
        state.currentSession = state.savedSessions[index];
      }

      this.saveState(state);
    }
  }
}