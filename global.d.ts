interface Window {
  onSpotifyWebPlaybackSDKReady: () => void;
  Spotify: typeof Spotify;
}

interface SpotifyPlayerState {
    context: {
        uri: string
        metadata: any
    }
    disallows: {
        pausing: boolean
        peeking_next: boolean
        peeking_prev: boolean
        resuming: boolean
        seeking: boolean
        skipping_next: boolean
        skipping_prev: boolean
    }
    paused: boolean
    position: number
    repeat_mode: number
    shuffle: boolean
    track_window: {
        current_track: any
        next_tracks: any[]
        previous_tracks: any[]
    }
    duration: number
}

declare namespace Spotify {
  interface Player {
      new (options: {
          name: string
          getOAuthToken: (cb: (token: string) => void) => void
          volume?: number
      }): Player

      connect(): Promise<boolean>
      disconnect(): void
      addListener(
          event: string,
          callback: (state: SpotifyPlayerState | null) => void
      ): boolean
      removeListener(event: string): boolean
      getCurrentState(): Promise<SpotifyPlayerState | null>
      setName(name: string): Promise<void>
      getVolume(): Promise<number>
      setVolume(volume: number): Promise<void>
      pause(): Promise<void>
      resume(): Promise<void>
      togglePlay(): Promise<void>
      seek(position_ms: number): Promise<void>
      previousTrack(): Promise<void>
      nextTrack(): Promise<void>
  }
}