'use client';

import { useState, useEffect } from 'react'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { SpotifyTrack } from '@/lib/spotify'
import { usePlayback } from '@/contexts/PlaybackContext'

interface DraggableTrackProps {
    track: SpotifyTrack
    alias: string
    isPlaying: boolean
    isSelected: boolean
    onPlay: () => void
    onAliasChange: (alias: string) => void
    isDragging?: boolean
}

export function DraggableTrack({
    track,
    alias,
    isPlaying,
    isSelected,
    onPlay,
    onAliasChange,
    isDragging = false
}: DraggableTrackProps) {
    const playback = usePlayback()
    const [isEditingAlias, setIsEditingAlias] = useState(false)
    const [tempAlias, setTempAlias] = useState(alias)

    // Update tempAlias when alias prop changes
    useEffect(() => {
        setTempAlias(alias)
    }, [alias])

    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging: isSortableDragging
    } = useSortable({
        id: track.id
    })

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isSortableDragging ? 0.5 : 1
    }

    const formatDuration = (ms: number) => {
        const minutes = Math.floor(ms / 60000)
        const seconds = Math.floor((ms % 60000) / 1000)
        return `${minutes}:${seconds.toString().padStart(2, '0')}`
    }

    const handlePlayClick = (e: React.MouseEvent) => {
        e.stopPropagation()
        if (playback.currentTrack?.uri === track.uri && !isPlaying) {
            // Resume from current position
            playback.resumeTrack()
        } else {
            // Start new track
            onPlay()
        }
    }

    const handlePauseClick = (e: React.MouseEvent) => {
        e.stopPropagation()
        playback.pauseTrack()
    }

    const handleAliasDoubleClick = (e: React.MouseEvent) => {
        e.stopPropagation()
        setIsEditingAlias(true)
        setTempAlias(alias)
    }

    const handleAliasSubmit = () => {
        onAliasChange(tempAlias)
        setIsEditingAlias(false)
    }

    const handleAliasKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            handleAliasSubmit()
        } else if (e.key === 'Escape') {
            setIsEditingAlias(false)
            setTempAlias(alias)
        }
    }

    return (
        <div
            ref={setNodeRef}
            style={style}
            className={`group flex items-center gap-2 p-2 rounded-lg transition-all ${
                isPlaying
                    ? 'bg-blue-500 text-white shadow-md'
                    : isSelected
                    ? 'bg-blue-100 border border-blue-300 text-blue-900'
                    : 'bg-white hover:bg-gray-50 border border-gray-200'
            } ${isSortableDragging ? 'shadow-xl ring-2 ring-blue-400' : ''}`}
        >
            {/* Drag handle */}
            <div
                {...attributes}
                {...listeners}
                className={`p-1 rounded cursor-move ${
                    isPlaying ? 'hover:bg-blue-600' : 'hover:bg-gray-200'
                } opacity-0 group-hover:opacity-100 transition-opacity`}
            >
                <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="currentColor"
                    className={isPlaying ? 'text-white' : 'text-gray-400'}
                >
                    <path d="M2 4.5a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0 4a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0 4a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z" />
                </svg>
            </div>

            {track.album.images[0] && (
                <img
                    src={track.album.images[0].url}
                    alt={track.album.name}
                    className="w-10 h-10 rounded flex-shrink-0"
                />
            )}

            <div className="flex-1 min-w-0">
                {isEditingAlias ? (
                    <input
                        type="text"
                        value={tempAlias}
                        onChange={(e) => setTempAlias(e.target.value)}
                        onBlur={handleAliasSubmit}
                        onKeyDown={handleAliasKeyDown}
                        className="font-medium text-sm w-full bg-white text-gray-900 border border-blue-300 rounded px-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        autoFocus
                        onClick={(e) => e.stopPropagation()}
                    />
                ) : (
                    <div
                        className={`font-medium text-sm truncate cursor-pointer ${
                            isPlaying ? 'text-white' : 'text-gray-900'
                        } ${alias ? 'italic' : ''}`}
                        onDoubleClick={handleAliasDoubleClick}
                        title={
                            alias
                                ? `Alias: ${alias} (Original: ${track.name})`
                                : `Double-click to add alias for: ${track.name}`
                        }
                    >
                        {alias || track.name}
                    </div>
                )}
                <div
                    className={`text-xs truncate ${
                        isPlaying ? 'text-blue-100' : 'text-gray-500'
                    }`}
                >
                    {track.artists.map((a) => a.name).join(', ')}
                </div>
            </div>

            <div
                className={`text-xs font-medium ${
                    isPlaying ? 'text-blue-100' : 'text-gray-500'
                }`}
            >
                {formatDuration(track.duration_ms)}
            </div>

            <div className="flex gap-1 items-center flex-shrink-0">
                {isPlaying ? (
                    <button
                        onClick={handlePauseClick}
                        className="p-1 rounded transition-colors bg-yellow-500 hover:bg-yellow-600 text-white text-xs"
                    >
                        <svg
                            width="12"
                            height="12"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                        >
                            <rect x="6" y="4" width="4" height="16" />
                            <rect x="14" y="4" width="4" height="16" />
                        </svg>
                    </button>
                ) : (
                    <button
                        onClick={handlePlayClick}
                        className="p-1 rounded transition-colors bg-green-500 hover:bg-green-600 text-white text-xs"
                    >
                        <svg
                            width="12"
                            height="12"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                        >
                            <path d="M8 5v14l11-7z" />
                        </svg>
                    </button>
                )}
            </div>
        </div>
    )
}
