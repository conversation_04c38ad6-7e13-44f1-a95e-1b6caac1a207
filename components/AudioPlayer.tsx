'use client';

import { useCallback } from 'react';
import { usePlayback } from '@/contexts/PlaybackContext';
import { TrackControls } from './TrackControls';

interface AudioPlayerProps {
  accessToken: string;
}

export function AudioPlayer({
  accessToken,
}: AudioPlayerProps) {
  const playback = usePlayback();

  const handleSeek = useCallback(
    (newPosition: number) => {
      playback.seekTo(newPosition);
    },
    [playback]
  );

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (!playback.currentTrack) {
    return (
      <div className="bg-gradient-to-b from-gray-900 to-black p-6">
        <div className="text-center text-gray-400 font-medium">
          Select a track to start playing
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-b from-gray-900 to-black p-6">
      <div className="flex items-center gap-4 mb-6">
        {playback.currentTrack.album.images[0] && (
          <img
            src={playback.currentTrack.album.images[0].url}
            alt={playback.currentTrack.album.name}
            className="w-20 h-20 rounded-lg shadow-lg"
          />
        )}
        <div className="flex-1 min-w-0">
          <div className="font-bold text-xl text-white truncate">{playback.currentTrack.name}</div>
          <div className="text-gray-300 text-sm truncate">
            {playback.currentTrack.artists.map((a) => a.name).join(', ')}
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <div className="relative group">
          <input
            type="range"
            min={0}
            max={playback.duration || 100}
            value={playback.position}
            onChange={(e) => handleSeek(Number(e.target.value))}
            className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
            style={{
              background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${(playback.position / (playback.duration || 1)) * 100}%, #4b5563 ${(playback.position / (playback.duration || 1)) * 100}%, #4b5563 100%)`
            }}
          />
        </div>
        <div className="flex justify-between text-xs text-gray-400 font-medium">
          <span>{formatTime(playback.position)}</span>
          <span>{formatTime(playback.duration)}</span>
        </div>
      </div>

      <div className="flex justify-center gap-4 mt-6">
        {playback.isPlaying ? (
          <button
            onClick={playback.pauseTrack}
            className="bg-yellow-500 text-white rounded-full p-4 hover:scale-110 transition-all shadow-lg hover:shadow-xl hover:bg-yellow-600"
          >
            <svg
              width="28"
              height="28"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <rect x="6" y="4" width="4" height="16" />
              <rect x="14" y="4" width="4" height="16" />
            </svg>
          </button>
        ) : (
          <button
            onClick={playback.resumeTrack}
            className="bg-green-500 text-white rounded-full p-4 hover:scale-110 transition-all shadow-lg hover:shadow-xl hover:bg-green-600"
          >
            <svg
              width="28"
              height="28"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path d="M8 5v14l11-7z" />
            </svg>
          </button>
        )}
      </div>
      <TrackControls track={playback.currentTrack} alias={playback.currentTrack.name} />
    </div>
  );
}
