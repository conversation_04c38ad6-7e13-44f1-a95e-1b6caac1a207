'use client';

import { useCallback } from 'react'
import { usePlayback } from '@/contexts/PlaybackContext'
import { TrackControls } from './TrackControls'

interface AudioPlayerProps {
    accessToken: string
}

export function AudioPlayer({}: AudioPlayerProps) {
    const playback = usePlayback()

    const handleSeek = useCallback(
        (newPosition: number) => {
            playback.seekTo(newPosition)
        },
        [playback]
    )

    const formatTime = (ms: number) => {
        const seconds = Math.floor(ms / 1000)
        const minutes = Math.floor(seconds / 60)
        const remainingSeconds = seconds % 60
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
    }

    // Stop slider movement when track finishes
    const effectivePosition =
        playback.position >= playback.duration && playback.duration > 0
            ? playback.duration
            : playback.position

    if (!playback.currentTrack) {
        return (
            <div className="bg-gradient-to-b from-gray-900 to-black p-6">
                <div className="text-center text-gray-400 font-medium">
                    Select a track to start playing
                </div>
            </div>
        )
    }

    return (
        <div className="bg-gradient-to-b from-gray-900 to-black p-6">
            <div className="flex items-center gap-4 mb-6">
                {playback.currentTrack.album.images[0] && (
                    <img
                        src={playback.currentTrack.album.images[0].url}
                        alt={playback.currentTrack.album.name}
                        className="w-20 h-20 rounded-lg shadow-lg"
                    />
                )}
                <div className="flex-1 min-w-0">
                    <div className="font-bold text-xl text-white truncate">
                        {playback.currentTrack.name}
                    </div>
                    <div className="text-gray-300 text-sm truncate">
                        {playback.currentTrack.artists
                            .map((a) => a.name)
                            .join(', ')}
                    </div>
                </div>
                {/* Move controls to the right of track name */}
                <div className="flex items-center gap-3">
                    {playback.isPlaying ? (
                        <button
                            onClick={playback.pauseTrack}
                            className="bg-yellow-500 text-white rounded-full p-3 hover:scale-110 transition-all shadow-lg hover:shadow-xl hover:bg-yellow-600"
                        >
                            <svg
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="currentColor"
                            >
                                <rect x="6" y="4" width="4" height="16" />
                                <rect x="14" y="4" width="4" height="16" />
                            </svg>
                        </button>
                    ) : (
                        <button
                            onClick={playback.resumeTrack}
                            className="bg-green-500 text-white rounded-full p-3 hover:scale-110 transition-all shadow-lg hover:shadow-xl hover:bg-green-600"
                        >
                            <svg
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="currentColor"
                            >
                                <path d="M8 5v14l11-7z" />
                            </svg>
                        </button>
                    )}
                    <button
                        onClick={playback.stopTrack}
                        className="bg-red-500 text-white rounded-full p-3 hover:scale-110 transition-all shadow-lg hover:shadow-xl hover:bg-red-600"
                    >
                        <svg
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                        >
                            <rect x="6" y="6" width="12" height="12" />
                        </svg>
                    </button>
                </div>
            </div>

            <div className="space-y-3">
                <div className="relative group">
                    {/* Custom styled slider track */}
                    <div className="w-full h-2 bg-gray-700 rounded-full relative overflow-hidden">
                        {/* Progress bar */}
                        <div
                            className="h-full bg-gradient-to-r from-blue-500 to-blue-400 rounded-full transition-all duration-150"
                            style={{
                                width: `${
                                    (effectivePosition /
                                        (playback.duration || 1)) *
                                    100
                                }%`
                            }}
                        />
                        {/* Slider thumb */}
                        <div
                            className="absolute top-1/2 w-4 h-4 bg-white rounded-full shadow-lg transform -translate-y-1/2 -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                            style={{
                                left: `${
                                    (effectivePosition /
                                        (playback.duration || 1)) *
                                    100
                                }%`
                            }}
                        />
                    </div>
                    {/* Invisible input for interaction */}
                    <input
                        type="range"
                        min={0}
                        max={playback.duration || 100}
                        value={effectivePosition}
                        onChange={(e) => handleSeek(Number(e.target.value))}
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                </div>
                <div className="flex justify-between text-xs text-gray-400 font-medium">
                    <span>{formatTime(effectivePosition)}</span>
                    <span>{formatTime(playback.duration)}</span>
                </div>
            </div>

            <TrackControls
                track={playback.currentTrack}
                alias={playback.currentTrack.name}
            />
        </div>
    )
}
