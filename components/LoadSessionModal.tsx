'use client';

import { Modal } from './Modal';
import { SavedSession } from '@/lib/persistence';

interface LoadSessionModalProps {
  isOpen: boolean;
  onClose: () => void;
  sessions: SavedSession[];
  onLoad: (session: SavedSession) => void;
  onDelete: (sessionId: string) => void;
}

export function LoadSessionModal({ 
  isOpen, 
  onClose, 
  sessions, 
  onLoad, 
  onDelete 
}: LoadSessionModalProps) {
  const handleLoad = (session: SavedSession) => {
    onLoad(session);
    onClose();
  };

  const handleDelete = (sessionId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm('Are you sure you want to delete this session?')) {
      onDelete(sessionId);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Load Session"
      size="md"
    >
      {sessions.length === 0 ? (
        <p className="text-gray-500 text-center py-4">
          No saved sessions for this playlist
        </p>
      ) : (
        <div className="space-y-2">
          {sessions.map((session) => (
            <div
              key={session.id}
              className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
              onClick={() => handleLoad(session)}
            >
              <div className="flex-1">
                <div className="font-medium text-gray-900">
                  {session.name}
                </div>
                <div className="text-sm text-gray-500">
                  {new Date(session.updatedAt).toLocaleString()}
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  {session.columns
                    .map((c) => `${c.title} (${c.trackIds.length})`)
                    .join(', ')}
                </div>
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => handleLoad(session)}
                  className="px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded text-sm"
                >
                  Load
                </button>
                <button
                  onClick={(e) => handleDelete(session.id, e)}
                  className="px-3 py-1 bg-red-500 hover:bg-red-600 text-white rounded text-sm"
                >
                  Delete
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </Modal>
  );
}
