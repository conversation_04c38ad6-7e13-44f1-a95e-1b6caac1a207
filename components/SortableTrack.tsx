'use client';

import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { SpotifyTrack } from '@/lib/spotify';

interface SortableTrackProps {
  track: SpotifyTrack;
  alias: string;
  isPlaying: boolean;
  onPlay: () => void;
  onAliasChange: (alias: string) => void;
}

export function SortableTrack({
  track,
  alias,
  isPlaying,
  onPlay,
  onAliasChange,
}: SortableTrackProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: track.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`flex items-center gap-2 p-2 rounded-lg transition-all cursor-move ${
        isPlaying ? 'bg-blue-500 text-white shadow-md' : 'bg-gray-50 hover:bg-gray-100 border border-gray-200'
      }`}
      {...attributes}
      {...listeners}
    >

      {track.album.images[0] && (
        <img
          src={track.album.images[0].url}
          alt={track.album.name}
          className="w-10 h-10 rounded flex-shrink-0"
        />
      )}

      <div className="flex-1 min-w-0">
        <div className={`font-medium text-sm truncate ${isPlaying ? 'text-white' : 'text-gray-900'}`}>
          {alias || track.name}
        </div>
        <div className={`text-xs truncate ${isPlaying ? 'text-blue-100' : 'text-gray-500'}`}>
          {track.artists.map((a) => a.name).join(', ')}
        </div>
      </div>

      <div className={`text-xs font-medium ${isPlaying ? 'text-blue-100' : 'text-gray-500'}`}>
        {formatDuration(track.duration_ms)}
      </div>

      <button
        onClick={onPlay}
        className={`p-1.5 rounded transition-colors flex-shrink-0 ${
          isPlaying
            ? 'bg-white text-blue-500 hover:bg-blue-100'
            : 'bg-blue-500 hover:bg-blue-600 text-white'
        }`}
      >
        {isPlaying ? (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <rect x="6" y="4" width="4" height="16" />
            <rect x="14" y="4" width="4" height="16" />
          </svg>
        ) : (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M8 5v14l11-7z" />
          </svg>
        )}
      </button>
    </div>
  );
}