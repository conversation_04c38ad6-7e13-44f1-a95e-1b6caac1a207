'use client';

import { Modal } from './Modal';

interface Bookmark {
  id: string;
  name: string;
  timestamp: number;
  createdAt: string;
}

interface BookmarksListModalProps {
  isOpen: boolean;
  onClose: () => void;
  trackName: string;
  bookmarks: Bookmark[];
  onJumpTo: (timestamp: number) => void;
  onDelete: (bookmarkId: string) => void;
}

export function BookmarksListModal({ 
  isOpen, 
  onClose, 
  trackName, 
  bookmarks, 
  onJumpTo, 
  onDelete 
}: BookmarksListModalProps) {
  const formatTime = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleJumpTo = (timestamp: number) => {
    onJumpTo(timestamp);
    onClose();
  };

  const handleDelete = (bookmarkId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm('Are you sure you want to delete this bookmark?')) {
      onDelete(bookmarkId);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Bookmarks for ${trackName}`}
      size="md"
    >
      {bookmarks.length === 0 ? (
        <p className="text-gray-700 text-center py-4">
          No bookmarks for this track
        </p>
      ) : (
        <div className="space-y-2">
          {bookmarks.map((bookmark) => (
            <div
              key={bookmark.id}
              className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
              onClick={() => handleJumpTo(bookmark.timestamp)}
            >
              <div className="flex-1">
                <div className="font-medium text-gray-900">
                  {bookmark.name}
                </div>
                <div className="text-sm text-gray-500">
                  {formatTime(bookmark.timestamp)} • {new Date(bookmark.createdAt).toLocaleString()}
                </div>
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => handleJumpTo(bookmark.timestamp)}
                  className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm"
                >
                  Jump To
                </button>
                <button
                  onClick={(e) => handleDelete(bookmark.id, e)}
                  className="px-3 py-1 bg-red-500 hover:bg-red-600 text-white rounded text-sm"
                >
                  Delete
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </Modal>
  );
}
