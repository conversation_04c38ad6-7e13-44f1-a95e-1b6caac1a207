'use client';

import { useState, useEffect } from 'react';
import { SpotifyTrack } from '@/lib/spotify';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { SortableTrack } from './SortableTrack';

interface PlaylistViewProps {
  tracks: SpotifyTrack[];
  currentTrack: string | null;
  onTrackSelect: (track: SpotifyTrack) => void;
  onTracksReorder: (tracks: SpotifyTrack[]) => void;
}

export function PlaylistView({
  tracks: initialTracks,
  currentTrack,
  onTrackSelect,
  onTracksReorder,
}: PlaylistViewProps) {
  const [tracks, setTracks] = useState(initialTracks);
  const [aliases, setAliases] = useState<Record<string, string>>({});

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    setTracks(initialTracks);
  }, [initialTracks]);

  useEffect(() => {
    const savedAliases = localStorage.getItem('trackAliases');
    if (savedAliases) {
      setAliases(JSON.parse(savedAliases));
    }
  }, []);

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = tracks.findIndex((track) => track.id === active.id);
      const newIndex = tracks.findIndex((track) => track.id === over?.id);

      const newTracks = arrayMove(tracks, oldIndex, newIndex);
      setTracks(newTracks);
      onTracksReorder(newTracks);
    }
  };

  const handleAliasChange = (trackId: string, alias: string) => {
    const newAliases = { ...aliases, [trackId]: alias };
    setAliases(newAliases);
    localStorage.setItem('trackAliases', JSON.stringify(newAliases));
  };

  return (
    <div className="flex-1 overflow-y-auto">
      <div className="p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Playlist</h2>
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={tracks.map((t) => t.id)}
            strategy={verticalListSortingStrategy}
          >
            <div className="space-y-3">
              {tracks.map((track) => (
                <SortableTrack
                  key={track.id}
                  track={track}
                  alias={aliases[track.id] || ''}
                  isPlaying={currentTrack === track.uri}
                  onPlay={() => onTrackSelect(track)}
                  onAliasChange={(alias) => handleAliasChange(track.id, alias)}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>
      </div>
    </div>
  );
}