'use client';

import { useState } from 'react';
import { Modal } from './Modal';

interface BookmarkModalProps {
  isOpen: boolean;
  onClose: () => void;
  trackName: string;
  currentTimestamp: number;
  onSave: (bookmarkName: string) => void;
}

export function BookmarkModal({ 
  isOpen, 
  onClose, 
  trackName, 
  currentTimestamp, 
  onSave 
}: BookmarkModalProps) {
  const [bookmarkName, setBookmarkName] = useState('');

  const formatTime = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleSave = () => {
    if (bookmarkName.trim()) {
      onSave(bookmarkName.trim());
      setBookmarkName('');
      onClose();
    }
  };

  const handleClose = () => {
    setBookmarkName('');
    onClose();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave();
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Add Bookmark"
      size="sm"
    >
      <div className="mb-4">
        <p className="text-sm text-gray-700 mb-2">
          Track: <span className="font-medium">{trackName}</span>
        </p>
        <p className="text-sm text-gray-700 mb-4">
          Timestamp: <span className="font-medium">{formatTime(currentTimestamp)}</span>
        </p>
      </div>

      <input
        type="text"
        value={bookmarkName}
        onChange={(e) => setBookmarkName(e.target.value)}
        placeholder="Enter bookmark name..."
        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 mb-4 text-gray-900 placeholder-gray-500"
        autoFocus
        onKeyDown={handleKeyDown}
      />

      <div className="flex justify-end gap-2">
        <button
          onClick={handleClose}
          className="px-4 py-2 text-gray-800 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
        >
          Cancel
        </button>
        <button
          onClick={handleSave}
          disabled={!bookmarkName.trim()}
          className="px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          Save Bookmark
        </button>
      </div>
    </Modal>
  );
}
