'use client';

import { useState, useEffect } from 'react';
import { usePlayback } from '@/contexts/PlaybackContext';
import { BookmarkModal } from './BookmarkModal'
import { BookmarksListModal } from './BookmarksListModal'
import { SpotifyTrack } from '@/lib/spotify'

interface TrackControlsProps {
    track: SpotifyTrack
    alias: string
}

export function TrackControls({ track, alias }: TrackControlsProps) {
    const playback = usePlayback()
    const [shownModal, setShownModal] = useState<
        'bookmark' | 'bookmarks-list' | undefined
    >()
    const [capturedTimestamp, setCapturedTimestamp] = useState<number>(0)
    const [bookmarks, setBookmarks] = useState<
        Array<{
            id: string
            name: string
            timestamp: number
            createdAt: string
        }>
    >([])

    const handleStopClick = (e: React.MouseEvent) => {
        e.stopPropagation()
        playback.stopTrack()
    }

    const handleAnnotateClick = (e: React.MouseEvent) => {
        e.stopPropagation()
        setCapturedTimestamp(playback.position) // Capture timestamp when modal opens
        setShownModal('bookmark')
    }

    const handleSaveBookmark = (bookmarkName: string) => {
        const bookmark = {
            id: Date.now().toString(),
            name: bookmarkName,
            timestamp: capturedTimestamp, // Use captured timestamp instead of current position
            createdAt: new Date().toISOString()
        }

        const bookmarksKey = `bookmarks_${track.id}`
        const existingBookmarks = JSON.parse(
            localStorage.getItem(bookmarksKey) || '[]'
        )

        const updatedBookmarks = [...existingBookmarks, bookmark]
        localStorage.setItem(bookmarksKey, JSON.stringify(updatedBookmarks))

        loadBookmarks()
    }

    const loadBookmarks = () => {
        const bookmarksKey = `bookmarks_${track.id}`
        const existingBookmarks = JSON.parse(
            localStorage.getItem(bookmarksKey) || '[]'
        )
        setBookmarks(existingBookmarks)
    }

    const handleDeleteBookmark = (bookmarkId: string) => {
        const bookmarksKey = `bookmarks_${track.id}`
        const updatedBookmarks = bookmarks.filter((b) => b.id !== bookmarkId)
        localStorage.setItem(bookmarksKey, JSON.stringify(updatedBookmarks))
        setBookmarks(updatedBookmarks)
    }

    const toggleBookmarksList = () => {
        if (shownModal !== 'bookmarks-list') {
            loadBookmarks()
        }
        setShownModal('bookmarks-list')
    }

    const handleJumpTo = (timestamp: number) => {
        playback.seekTo(timestamp)
    }

    useEffect(() => {
        loadBookmarks()
    }, [track.id])

    return (
        <div className="flex justify-center gap-3 mt-6">
            <button
                onClick={handleStopClick}
                disabled={playback.currentTrack?.uri !== track.uri}
                className="bg-red-500 text-white rounded-full p-3 hover:scale-110 transition-all shadow-lg hover:shadow-xl hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
            >
                <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                >
                    <rect x="6" y="6" width="12" height="12" />
                </svg>
            </button>

            <button
                onClick={handleAnnotateClick}
                disabled={playback.currentTrack?.uri !== track.uri}
                className="bg-purple-500 text-white rounded-full p-3 hover:scale-110 transition-all shadow-lg hover:shadow-xl hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
                title="Add bookmark"
            >
                <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                >
                    <path d="M17 3H7c-1.1 0-2 .9-2 2v16l7-3 7 3V5c0-1.1-.9-2-2-2z" />
                </svg>
            </button>

            <button
                onClick={toggleBookmarksList}
                className={`rounded-full p-3 hover:scale-110 transition-all shadow-lg hover:shadow-xl disabled:hover:scale-100 ${
                    bookmarks.length > 0
                        ? 'bg-indigo-500 hover:bg-indigo-600 text-white'
                        : 'bg-gray-400 text-gray-200 cursor-not-allowed opacity-50'
                }`}
                disabled={bookmarks.length === 0}
                title={`${bookmarks.length} bookmarks`}
            >
                <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                >
                    <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z" />
                </svg>
            </button>

            <BookmarkModal
                isOpen={shownModal === 'bookmark'}
                onClose={() => setShownModal(undefined)}
                trackName={alias || track.name}
                currentTimestamp={capturedTimestamp} // Use captured timestamp
                onSave={handleSaveBookmark}
            />

            <BookmarksListModal
                isOpen={shownModal === 'bookmarks-list'}
                onClose={() => setShownModal(undefined)}
                trackName={alias || track.name}
                bookmarks={bookmarks}
                onJumpTo={handleJumpTo}
                onDelete={handleDeleteBookmark}
            />
        </div>
    )
}