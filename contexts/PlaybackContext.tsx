'use client';

import React, { createContext, useContext, useState, useCallback, useRef, useEffect } from 'react';
import { SpotifyTrack, SpotifyAPI } from '@/lib/spotify'

interface PlaybackState {
    currentTrack: SpotifyTrack | null
    isPlaying: boolean
    position: number
    duration: number
    player: Spotify.Player | null
    deviceId: string | null
}

interface PlaybackContextType {
    // State
    currentTrack: SpotifyTrack | null
    isPlaying: boolean
    position: number
    duration: number
    deviceId: string | null

    // Actions
    playTrack: (track: SpotifyTrack) => void
    pauseTrack: () => void
    stopTrack: () => void
    resumeTrack: () => void
    seekTo: (position: number) => void

    // Player management
    initializePlayer: (accessToken: string) => void
    setPlayerState: (state: Partial<PlaybackState>) => void
}

const PlaybackContext = createContext<PlaybackContextType | null>(null)

export const usePlayback = () => {
    const context = useContext(PlaybackContext)
    if (!context) {
        throw new Error('usePlayback must be used within a PlaybackProvider')
    }
    return context
}

interface PlaybackProviderProps {
    children: React.ReactNode
}

export const PlaybackProvider: React.FC<PlaybackProviderProps> = ({
    children
}) => {
    const [currentTrack, setCurrentTrack] = useState<SpotifyTrack | null>(null)
    const [isPlaying, setIsPlaying] = useState(false)
    const [position, setPosition] = useState(0)
    const [duration, setDuration] = useState(0)
    const [deviceId, setDeviceId] = useState<string | null>(null)
    const playerRef = useRef<Spotify.Player | null>(null)
    const intervalRef = useRef<NodeJS.Timeout | null>(null)
    const accessTokenRef = useRef<string | null>(null)
    const spotifyAPIRef = useRef<SpotifyAPI | null>(null)

    // Update position while playing
    useEffect(() => {
        if (isPlaying && duration > 0) {
            intervalRef.current = setInterval(() => {
                setPosition((prev) => {
                    const newPosition = prev + 1000
                    return newPosition >= duration ? duration : newPosition
                })
            }, 1000)
        } else {
            if (intervalRef.current) {
                clearInterval(intervalRef.current)
                intervalRef.current = null
            }
        }

        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current)
            }
        }
    }, [isPlaying, duration])

    const initializePlayer = useCallback((accessToken: string) => {
        accessTokenRef.current = accessToken
        spotifyAPIRef.current = new SpotifyAPI(accessToken)

        if (playerRef.current) {
            return // Already initialized
        }

        // Load Spotify Web Playback SDK
        if (!window.Spotify) {
            const script = document.createElement('script')
            script.src = 'https://sdk.scdn.co/spotify-player.js'
            script.async = true
            document.body.appendChild(script)
        }

        const initializeSpotifyPlayer = () => {
            const player = new window.Spotify.Player({
                name: 'Spotify Web Player',
                getOAuthToken: (cb: (token: string) => void) => {
                    cb(accessToken)
                },
                volume: 0.5
            })

            player.addListener(
                'ready',
                ({ device_id }: { device_id: string }) => {
                    console.log('Ready with Device ID', device_id)
                    setDeviceId(device_id)
                }
            )

            player.addListener(
                'player_state_changed',
                (state: SpotifyPlayerState | null) => {
                    if (!state) return
                    setPosition(state.position)
                    setDuration(state.duration)
                }
            )

            player.connect()
            playerRef.current = player
        }

        if (window.Spotify) {
            initializeSpotifyPlayer()
        } else {
            window.onSpotifyWebPlaybackSDKReady = initializeSpotifyPlayer
        }
    }, [])

    const playTrack = useCallback(
        async (track: SpotifyTrack) => {
            if (!deviceId || !spotifyAPIRef.current) return

            try {
                await spotifyAPIRef.current.play(deviceId, track.uri, 0)
                setCurrentTrack(track)
                setIsPlaying(true)
                setPosition(0)
            } catch (error) {
                console.error('Failed to play track:', error)
            }
        },
        [deviceId]
    )

    const pauseTrack = useCallback(() => {
        if (!playerRef.current) return

        playerRef.current.pause().then(() => {
            setIsPlaying(false)
        })
    }, [])

    const resumeTrack = useCallback(() => {
        if (!playerRef.current) return

        playerRef.current.resume().then(() => {
            setIsPlaying(true)
        })
    }, [])

    const stopTrack = useCallback(() => {
        if (!playerRef.current) return

        playerRef.current.seek(0).then(() => {
            playerRef.current?.pause().then(() => {
                setIsPlaying(false)
                setPosition(0)
            })
        })
    }, [])

    const seekTo = useCallback((newPosition: number) => {
        if (!playerRef.current) return

        playerRef.current.seek(newPosition).then(() => {
            setPosition(newPosition)
        })
    }, [])

    const setPlayerState = useCallback((state: Partial<PlaybackState>) => {
        if (state.currentTrack !== undefined)
            setCurrentTrack(state.currentTrack)
        if (state.isPlaying !== undefined) setIsPlaying(state.isPlaying)
        if (state.position !== undefined) setPosition(state.position)
        if (state.duration !== undefined) setDuration(state.duration)
        if (state.deviceId !== undefined) setDeviceId(state.deviceId)
        if (state.player !== undefined) playerRef.current = state.player
    }, [])

    const value: PlaybackContextType = {
        currentTrack,
        isPlaying,
        position,
        duration,
        deviceId,
        playTrack,
        pauseTrack,
        stopTrack,
        resumeTrack,
        seekTo,
        initializePlayer,
        setPlayerState
    }

    return (
        <PlaybackContext.Provider value={value}>
            {children}
        </PlaybackContext.Provider>
    )
}